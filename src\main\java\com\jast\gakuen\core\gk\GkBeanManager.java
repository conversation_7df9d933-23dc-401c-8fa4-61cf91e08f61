/*
 * GkBeanManager.java
 *
 * 著作権  ：Copyright Japan System Techniques Co., Ltd. All Rights Reserved.
 * 会社名  ：日本システム技術株式会社
 */
package com.jast.gakuen.core.gk;

import com.jast.gakuen.core.common.ICdiBeanRefresh;
import com.jast.gakuen.core.common.util.RxLogger;
import com.jast.gakuen.core.common.util.UtilCdi;
import com.jast.gakuen.core.common.util.UtilFaces;
import com.jast.gakuen.core.gk.constant.GkWindowManagedKbn;
import com.jast.gakuen.core.gk.dto.GkDispFormDTO;
import com.jast.gakuen.core.gk.dto.GkOpenWindowDTO;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.enterprise.context.SessionScoped;
import javax.inject.Named;
import lombok.Getter;

/**
 * GAKUEN用ビーン管理クラス。<br>
 * <br>
 * 画面遷移、別ウィンドウのオープンまたはクローズ時のビーン管理を行う。
 *
 * <AUTHOR> System Techniques Co.,Ltd.
 */
@Named
@SessionScoped
public class GkBeanManager implements ICdiBeanRefresh {

	/**
	 * ロガー
	 */
	private static final RxLogger LOGGER = new RxLogger(GkBeanManager.class);

	/**
	 * ウィンドウ情報を管理するマップ。
	 */
	@Getter
	private final Map<String, GkOpenWindowDTO> windowMap = new HashMap<>();

	/**
	 * 画面情報を管理するマップ。
	 */
	@Getter
	private final Map<String, GkDispFormDTO> formMap = new HashMap<>();

	/**
	 * インスタンスを破棄する時に処理される。
	 *
	 * @throws Exception 例外
	 */
	@Override
	public void refresh() throws Exception {
	}

	/**
	 * 機能IDに紐付く管理ビーンを破棄する。
	 *
	 * @param funcId 機能ID
	 */
	public void destroyBeanByFuncId(final String funcId) {
		// 親ウィンドウのウィンドウ名は「機能ID」であるから取得できた場合は破棄する。
		this.destroyBeansWithWindow(this.windowMap.get(funcId));
	}

	/**
	 * 進む処理に伴うビーンの管理処理。<br>
	 * 次画面の画面情報を管理する。<br>
	 * 次画面のビーンが既に存在する場合は破棄する。
	 *
	 * @param currentWindow 現在のウィンドウ情報
	 * @param formId 遷移先画面ID
	 */
	void managed4Forword(final GkOpenWindowDTO currentWindow, final String formId) {
		// 移動前に子ウィンドウ情報を破棄する
		this.destroyBeansOfChildWindow();

		// 遷移先画面情報を取得し、存在すれば破棄する（基本的にあり得ない）
		GkDispFormDTO form = this.formMap.get(formId);
		if (form != null) {
			this.destroyBeansWithForm(form);
		}

		// ウィンドウ情報に遷移先画面IDを追加
		currentWindow.addFormId(formId);

		// 遷移先画面情報を生成
		form = new GkDispFormDTO();
		form.setFormId(formId);
		form.setPreFormId(UtilFaces.getFormId());
		form.setBelongWindowName(currentWindow.getWindowName());

		// マップに保存
		this.formMap.put(form.getFormId(), form);
	}

	/**
	 * 戻る処理に伴うビーンの管理処理。<br>
	 * 前画面に戻ることで不要となったバッキングビーンを破棄する。<br>
	 * 破棄対象の画面からオープンした子ウィンドウも破棄対象とする。
	 *
	 * @param formId 遷移先画面ID
	 */
	void managed4Backword(final String formId) {
		// 移動前に子ウィンドウ情報を破棄する
		this.destroyBeansOfChildWindow();

		// 遷移先画面情報を取得
		GkDispFormDTO destForm = this.formMap.get(formId);
		if (destForm == null) {
			return;     // nullの場合は何もしない
		}

		// 遷移先画面が属するウィンドウ情報を取得
		GkOpenWindowDTO window = this.windowMap.get(destForm.getBelongWindowName());
		if (window == null) {
			return;     // nullの場合は何もしない
		}

		// 戻り先画面以降の画面は破棄（1画面ずつ戻るため、対象は1画面しかあり得ない）
		boolean remove = false;
		List<String> removeIds = new ArrayList<>();
		for (String tgtFormId : window.getFormIds()) {
			if (remove) {
				GkDispFormDTO tgtForm = this.formMap.get(tgtFormId);
				this.destroyBeansWithForm(tgtForm);
				removeIds.add(tgtFormId);
			}
			if (tgtFormId.equals(formId)) {
				remove = true;
			}
		}

		// 破棄した画面IDは削除
		window.getFormIds().removeAll(removeIds);
	}

	/**
	 * ウィンドウオープンに伴うビーンの管理処理。<br>
	 * <br>
	 * オープン対象のウィンドウ情報、画面情報を管理する。<br>
	 * オープン対象のウィンドウが既に存在する場合、これに関連するすべてのビーン（遷移画面、子画面含む）を破棄する。
	 *
	 * @param formId 表示する画面ID
	 * @param mngKbn ウィンドウの管理区分
	 */
	void managed4OpenWindow(final String formId, final GkWindowManagedKbn mngKbn) {

		// 子ウィンドウ（＝自ら管理する）か否か
		boolean isChild = (GkWindowManagedKbn.Self == mngKbn);

		if (isChild) {
			// 子ウィンドウは複数起動を認めない ⇒ 既存の子ウィンドウは破棄する
			this.destroyBeansOfChildWindow();
		}

		// ### 同名のウィンドウを破棄する ###
		// オープン対象の画面情報が存在する場合は取得
		GkDispFormDTO form = this.formMap.get(formId);

		// // 既存のウィンドウ情報を取得し、破棄する
		this.destroyBeansWithForm(form);   // この場合はformはnullを想定しているが、念のため処理を通す

		// ### 新たにウィンドウ情報、画面情報を生成 ###
		String newWindowName = this.decideWindowName(formId, isChild);      // 新ウィンドウ名称
		String parentFormId = (isChild) ? UtilFaces.getFormId() : null;     // 親画面ID（呼び出し元画面ID）

		// ウィンドウ情報を生成（親画面の場合は parentForm は設定不要）
		GkOpenWindowDTO window = new GkOpenWindowDTO();
		window.setWindowName(newWindowName);
		window.addFormId(formId);
		window.setParentFormId(parentFormId);

		// 画面情報を生成
		form = new GkDispFormDTO();
		form.setFormId(formId);
		form.setBelongWindowName(window.getWindowName());

		// マップに保存
		this.windowMap.put(window.getWindowName(), window);
		this.formMap.put(form.getFormId(), form);
	}

	/**
	 * ウィンドウクローズに伴うビーンの管理処理。<br>
	 * ウィンドウに紐付く全画面のバッキングビーンを破棄する。
	 *
	 */
	void managed4CloseWindow() {
		// 現在の画面IDを取得。
		String currentFormId = UtilFaces.getFormId();

		// 画面情報を取得し、破棄する
		GkDispFormDTO form = this.formMap.get(currentFormId);
		if (form != null) {
			this.destroyBeansWithWindow(this.windowMap.get(form.getBelongWindowName()));
		}
	}

	/**
	 * ウィンドウクローズに伴うビーンの管理処理。<br>
	 * フォーム情報のみ破棄する。
	 *
	 */
	void managed4CloseForm() {
		// 現在の画面IDを取得。
		String currentFormId = UtilFaces.getFormId();

		// 画面情報を取得し、破棄する
		GkDispFormDTO form = this.formMap.get(currentFormId);
		if (form != null) {
			this.destroyBeansWithForm(form);
		}
	}

	/**
	 * 子画面IDを取得する。
	 *
	 * @param formId 親画面ID
	 * @return 子画面ID
	 */
	String getChildFormId(final String formId) {
		for (GkOpenWindowDTO window : this.windowMap.values()) {
			if (formId.equals(window.getParentFormId())) {
				return window.getFormIds().get(window.getFormIds().size() - 1);
			}
		}
		return null;
	}

	/**
	 * 現在画面の子ウィンドウ情報を破棄する。
	 */
	private void destroyBeansOfChildWindow() {
		// 現在（呼び出し元ウィンドウ）の画面ID
		String currentFormId = UtilFaces.getFormId();
		for (GkOpenWindowDTO window : this.windowMap.values()) {
			if (currentFormId.equals(window.getParentFormId())) {
				// 現在画面を親に持つウィンドウ（＝子ウィンドウ）を破棄する
				this.destroyBeansWithWindow(window);
			}
		}
	}

	/**
	 * ウィンドウ情報に紐付く全画面のバッキングビーンを破棄する。<br>
	 * 当該ウィンドウ内で遷移した画面も破棄対象となる。
	 *
	 * @param window ウィンドウ情報
	 */
	private void destroyBeansWithWindow(final GkOpenWindowDTO window) {
		// nullの場合は何もしない
		if (window == null) {
			return;
		}

		for (String formId : window.getFormIds()) {
			GkDispFormDTO form = this.formMap.get(formId);
			this.destroyBeansWithForm(form);
		}
		this.formMap.remove(window.getWindowName());    // ウィンドウ情報の破棄
	}

	/**
	 * 画面情報と紐付くバッキングビーンを破棄する。<br>
	 * 当該画面よりオープンした子ウィンドウも破棄対象となる。
	 *
	 * @param form 画面情報
	 */
	private void destroyBeansWithForm(final GkDispFormDTO form) {
		// nullの場合は何もしない
		if (form == null) {
			return;
		}

		// 当該画面よりオープンされた子ウィンドウを取得
		List<GkOpenWindowDTO> childWindow = this.windowMap.values().stream().filter(f -> form.getFormId().equals(f.getParentFormId()))
				.collect(Collectors.toList());

		// 子ウィンドウを削除
		for (GkOpenWindowDTO tgtWin : childWindow) {
			this.destroyBeansWithWindow(tgtWin);
		}

		this.destroyBean(form.getFormId());         // バッキングビーンの破棄
		this.formMap.remove(form.getFormId());      // 画面情報の破棄
	}

	/**
	 * 指定した画面IDのバッキングビーンのセッション情報を破棄する。
	 *
	 * @param formId 画面ID
	 */
	private void destroyBean(final String formId) {
		// バッキングビーンのネームを取得する
		final String beanName = formId + "Bean";
		// セッション情報を破棄する
		UtilCdi.destroy(beanName);
		// トレースログを出力する。
		LOGGER.debug(String.format("Close the window of 「%s」", formId));
	}

	/**
	 * ウィンドウ名を決定する。<br>
	 * 親画面であれば機能ID、子画面であれば画面IDをウィンドウ名とする。
	 *
	 * @param formId 画面ID
	 * @param child 子画面起動か否か
	 * @return ウィンドウ名
	 */
	private String decideWindowName(final String formId, final boolean child) {
		// 画面IDから機能IDを取得
		// formIdが空文字列または6文字未満の場合はそのまま返す
		if (formId == null || formId.length() < 6) {
			return formId;
		}
		String funcId = formId.substring(0, 6);
		return (child) ? formId : funcId;
	}
}
