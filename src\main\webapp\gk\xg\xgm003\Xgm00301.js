/*
 * Ghd00802.js
 *
 * 著作権  ：Copyright Japan System Techniques Co., Ltd. All Rights Reserved.
 * 会社名  ：日本システム技術株式会社
 */

var openWindowOrg = openWindow;
var caller = {};

/**
 * 別ウィンドウを起動する。
 * 
 * @param {String} path 表示する画面のパス
 * @param {String} name ウィンドウ名
 * @param {Object} option オプション（オープンする条件）
 */
openWindow = function (path, name, option) {
    openWindowOrg(path, name, option);

    if (option.openId) {
        caller[name] = option.openId;
    }
};

/**
 * 子画面クローズ時の選択情報受取処理
 *
 * @param formId 子画面のフォームID
 */
function receive(formId) {

    if (formId === 'Pka20101') {
        if (caller['Pka20101'] === 'Xgm00301T01:shshnkCdSearch') {
           //出身校検索
           console.log('Calling t1_receiveShshnk - Fixed version');
            t1_receiveShshnk();
        }
    }
    if (formId === 'Ghb20101') {
        
        if (caller['Ghb20101'] === 'gkIndvDesignation:openChildWindow') {
            receiveIndvDesignationGaksei();
        }
        if (caller['Ghb20101'] === 'Xgm00301T03:openChildWindow') {
            //学生検索
            t4_receiveGaksekiCd();
        }
    }
    if (formId === 'Pkz20501') {
        if (caller['Pkz20501'] === 'Xgm00301T01:doClickPkGkfrCondition') {
            // 自由設定出力条件設定画面
            t1_receivePkGkfrCondition();
        }
        if (caller['Pkz20501'] === 'Xgm00301T01:doClickGhGkfrCondition') {
            // 自由設定出力条件設定画面
            t1_receiveGhGkfrCondition();
        }
    }
}

/**
 * 子画面(呼出先)からのcallback
 * 
 * @param {Object} param パラメータ
 */
function pushProc(param) {
    if (param.formId === "Pky20302") {
       updOutputItemsArea();
    }
}
